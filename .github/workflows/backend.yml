name: Backend Testing
on:
    push:
        paths:
            - hyperglass/**
            - .github/**
    pull_request:

jobs:
    backend:
        name: Backend Tests
        strategy:
            fail-fast: false
            matrix:
                node-version: [20.x]
                pnpm-version: [8]
                redis-version: [latest]
                python-version: ["3.11", "3.12"]
                os: [ubuntu-latest]
        runs-on: ${{ matrix.os }}

        steps:
            - name: Git Checkout
              uses: actions/checkout@v3

            - name: Install Python
              uses: actions/setup-python@v5
              with:
                  python-version: ${{ matrix.python-version }}

            - name: Setup Rye
              uses: sksat/setup-rye@v0.23.1

            - name: Install Node
              uses: actions/setup-node@v4
              with:
                  node-version: ${{ matrix.node-version }}

            - name: Install PNPM
              uses: pnpm/action-setup@v3
              with:
                  version: ${{ matrix.pnpm-version }}

            - name: Start Redis
              uses: supercharge/redis-github-action@1.7.0
              with:
                  redis-version: ${{ matrix.redis-version }}

            - name: Prepare
              run: |
                  sudo apt-get install libcairo2-dev -y
                  mkdir -p "$HOME/hyperglass"
                  echo "HYPERGLASS_APP_PATH=$HOME/hyperglass" >> $GITHUB_ENV
                  echo "HYPERGLASS_HOST=127.0.0.1" >> $GITHUB_ENV
                  echo "HYPERGLASS_PORT=8001" >> $GITHUB_ENV

            - name: Install
              run: rye sync

            - name: Activate virtualenv
              run: |
                  . .venv/bin/activate
                  echo PATH=$PATH >> $GITHUB_ENV

            - name: Lint (Rye)
              run: rye lint

            - name: Tests (PyTest)
              run: pytest hyperglass --ignore hyperglass/plugins/external

            - name: Run hyperglass
              run: ".tests/ga-backend-app.sh"
