name: Frontend Testing
on:
    push:
        paths:
            - hyperglass/ui/**
            - .github/**
    pull_request:

jobs:
    frontend:
        name: Frontend Tests
        strategy:
            fail-fast: false
            matrix:
                node-version: [20.x]
                pnpm-version: [8]
                os: [ubuntu-latest]
        runs-on: ${{ matrix.os }}
        env:
            working-directory: ./hyperglass/ui
        steps:
            - name: Git Checkout
              uses: actions/checkout@v3

            - name: Install Node
              uses: actions/setup-node@v4
              with:
                  node-version: ${{ matrix.node-version }}

            - name: Install PNPM
              uses: pnpm/action-setup@v3
              with:
                  version: ${{ matrix.pnpm-version }}

            - name: Install Dependencies
              working-directory: ${{ env.working-directory }}
              run: pnpm install

            - name: Create empty hyperglass.json
              working-directory: ${{ env.working-directory }}
              run: echo '{}' > hyperglass.json

            - name: Formatting
              working-directory: ${{ env.working-directory }}
              run: pnpm run format:check

            - name: Lint
              working-directory: ${{ env.working-directory }}
              run: pnpm run lint

            - name: Check Types
              working-directory: ${{ env.working-directory }}
              run: pnpm run typecheck

            - name: Tests
              working-directory: ${{ env.working-directory }}
              run: pnpm run test
