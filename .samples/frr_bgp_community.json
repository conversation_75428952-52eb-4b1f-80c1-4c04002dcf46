{"vrfId": 0, "vrfName": "default", "tableVersion": 32856694, "routerId": "***********", "defaultLocPrf": 100, "localAS": 14525, "routes": {"***********/24": [{"valid": true, "bestpath": true, "pathFrom": "internal", "prefix": "***********", "prefixLen": 24, "network": "***********/24", "med": 0, "metric": 0, "localpref": 450, "locPrf": 450, "weight": 200, "peerId": "***********", "aspath": "", "path": "", "origin": "IGP", "nexthops": [{"ip": "***********", "afi": "ipv4", "used": true}]}], "***********/23": [{"valid": true, "pathFrom": "internal", "prefix": "***********", "prefixLen": 23, "network": "***********/23", "med": 0, "metric": 0, "localpref": 450, "locPrf": 450, "weight": 200, "peerId": "***********0", "aspath": "", "path": "", "origin": "IGP", "nexthops": [{"ip": "***********0", "afi": "ipv4", "used": true}]}, {"valid": true, "pathFrom": "internal", "prefix": "***********", "prefixLen": 23, "network": "***********/23", "med": 0, "metric": 0, "localpref": 450, "locPrf": 450, "weight": 200, "peerId": "***********", "aspath": "", "path": "", "origin": "IGP", "nexthops": [{"ip": "***********", "afi": "ipv4", "used": true}]}, {"valid": true, "bestpath": true, "pathFrom": "internal", "prefix": "***********", "prefixLen": 23, "network": "***********/23", "med": 0, "metric": 0, "localpref": 450, "locPrf": 450, "weight": 200, "peerId": "***********", "aspath": "", "path": "", "origin": "IGP", "nexthops": [{"ip": "***********", "afi": "ipv4", "used": true}]}]}}