<rpc-reply xmlns:junos="http://xml.juniper.net/junos/18.2R3/junos">
	<route-information xmlns="http://xml.juniper.net/junos/18.2R3/junos-routing">
		<!-- keepalive -->
		<route-table>
			<table-name>
				inet.0
			</table-name>
			<destination-count>
				851482
			</destination-count>
			<total-route-count>
				3145808
			</total-route-count>
			<active-route-count>
				851445
			</active-route-count>
			<holddown-route-count>
				1
			</holddown-route-count>
			<hidden-route-count>
				94
			</hidden-route-count>
			<rt junos:style="detail">
				<rt-destination>
					*******
				</rt-destination>
				<rt-prefix-length>
					24
				</rt-prefix-length>
				<rt-entry-count junos:format="4 entries">
					4
				</rt-entry-count>
				<rt-announced-count>
					1
				</rt-announced-count>
				<rt-entry>
					<active-tag>
						*
					</active-tag>
					<current-active />
					<last-active />
					<protocol-name>
						BGP
					</protocol-name>
					<preference>
						170
					</preference>
					<preference2>
						-176
					</preference2>
					<nh-type>
						Router
					</nh-type>
					<nh-index>
						1007
					</nh-index>
					<nh-address>
						0x36d576ac
					</nh-address>
					<nh-reference-count>
						1337511
					</nh-reference-count>
					<nh-kernel-id>
						0
					</nh-kernel-id>
					<gateway>
						**************
					</gateway>
					<nh junos:indent="16">
						<nh-string>
							Next hop
						</nh-string>
						<to>
							**************
						</to>
						<via>
							xe-0/1/1.0
						</via>
						<selected-next-hop />
						<session>
							4e3
						</session>
					</nh>
					<rt-entry-state>
						Active Ext
					</rt-entry-state>
					<local-as>
						14525
					</local-as>
					<peer-as>
						1299
					</peer-as>
					<age junos:seconds="2634431">
						4w2d 11:47:11
					</age>
					<validation-state>
						valid
					</validation-state>
					<med-plus-igp>
						0
					</med-plus-igp>
					<task-name>
						BGP_1299.**************
					</task-name>
					<announce-bits>
						5
					</announce-bits>
					<announce-tasks>
						0-KRT 6-BGP_RT_Background 8-Resolve tree 3 10-Aggregate 12-RT
					</announce-tasks>
					<as-path>
						AS path: 1299 13335 I
						Aggregator: 13335 141.101.72.1
						AS path: Recorded
					</as-path>
					<bgp-path-attributes>
						<attr-as-path-effective>
							<aspath-effective-string>
								AS path:
							</aspath-effective-string>
							<attr-value>
								1299 13335 I
							</attr-value>
							<flags>
							</flags>
						</attr-as-path-effective>
						<attr-aggregator>
							<attr-length>
								11
							</attr-length>
							<attr-value>
								<aggr-as-number>
									13335
								</aggr-as-number>
								<aggr-router-id>
									141.101.72.1
								</aggr-router-id>
							</attr-value>
						</attr-aggregator>
					</bgp-path-attributes>
					<communities>
						<community>
							1299:35000
						</community>
						<community>
							14525:0
						</community>
						<community>
							14525:41
						</community>
						<community>
							14525:600
						</community>
						<community>
							14525:1021
						</community>
						<community>
							14525:2840
						</community>
						<community>
							14525:3001
						</community>
						<community>
							14525:4001
						</community>
						<community>
							14525:9003
						</community>
					</communities>
					<bgp-rt-flag>
						Accepted
					</bgp-rt-flag>
					<local-preference>
						175
					</local-preference>
					<peer-id>
						************
					</peer-id>
				</rt-entry>
				<rt-entry>
					<active-tag>
					</active-tag>
					<protocol-name>
						BGP
					</protocol-name>
					<preference>
						200
					</preference>
					<preference2>
						-251
					</preference2>
					<nh-type>
						Indirect
					</nh-type>
					<nh-index>
						0
					</nh-index>
					<nh-address>
						0x159d47bc
					</nh-address>
					<nh-reference-count>
						546747
					</nh-reference-count>
					<nh-kernel-id>
						0
					</nh-kernel-id>
					<gateway>
						***********
					</gateway>
					<nh-type>
						Router
					</nh-type>
					<nh-index>
						897
					</nh-index>
					<nh junos:indent="16">
						<nh-string>
							Next hop
						</nh-string>
						<to>
							***********
						</to>
						<via>
							et-0/0/2.3601
						</via>
						<selected-next-hop />
						<label-element>
							0x4261200
						</label-element>
						<label-element-parent>
							0x0
						</label-element-parent>
						<label-element-refcount>
							4
						</label-element-refcount>
						<label-element-childcount>
							2
						</label-element-childcount>
						<label-element-lspid>
							0
						</label-element-lspid>
						<session>
							53c
						</session>
					</nh>
					<protocol-nh junos:indent="16">
						<to>
							***********
						</to>
						<indirect-nh>
							0xa010e00 1048620 INH Session ID: 0x53e
						</indirect-nh>
					</protocol-nh>
					<rt-entry-state>
						Int Ext
					</rt-entry-state>
					<inactive-reason>
						Route Preference
					</inactive-reason>
					<local-as>
						14525
					</local-as>
					<peer-as>
						14525
					</peer-as>
					<age junos:seconds="1766791">
						2w6d 10:46:31
					</age>
					<metric>
						0
					</metric>
					<metric2>
						1000
					</metric2>
					<validation-state>
						unverified
					</validation-state>
					<med-plus-igp>
						1000
					</med-plus-igp>
					<task-name>
						BGP_14525.***********
					</task-name>
					<as-path>
						AS path: 13335 I
						Aggregator: 13335 172.68.129.1
						AS path: Recorded
					</as-path>
					<bgp-path-attributes>
						<attr-as-path-effective>
							<aspath-effective-string>
								AS path:
							</aspath-effective-string>
							<attr-value>
								13335 I
							</attr-value>
							<flags>
							</flags>
						</attr-as-path-effective>
						<attr-aggregator>
							<attr-length>
								11
							</attr-length>
							<attr-value>
								<aggr-as-number>
									13335
								</aggr-as-number>
								<aggr-router-id>
									172.68.129.1
								</aggr-router-id>
							</attr-value>
						</attr-aggregator>
					</bgp-path-attributes>
					<communities>
						<community>
							13335:10232
						</community>
						<community>
							13335:19000
						</community>
						<community>
							13335:20050
						</community>
						<community>
							13335:20500
						</community>
						<community>
							13335:20530
						</community>
						<community>
							14525:0
						</community>
						<community>
							14525:20
						</community>
						<community>
							14525:600
						</community>
						<community>
							14525:1021
						</community>
						<community>
							14525:2840
						</community>
						<community>
							14525:3003
						</community>
						<community>
							14525:4003
						</community>
						<community>
							14525:9009
						</community>
					</communities>
					<bgp-rt-flag>
						Accepted
					</bgp-rt-flag>
					<local-preference>
						250
					</local-preference>
					<peer-id>
						***********
					</peer-id>
				</rt-entry>
				<rt-entry>
					<active-tag>
					</active-tag>
					<protocol-name>
						BGP
					</protocol-name>
					<preference>
						200
					</preference>
					<preference2>
						-251
					</preference2>
					<nh-type>
						Indirect
					</nh-type>
					<nh-index>
						0
					</nh-index>
					<nh-address>
						0x20040a2c
					</nh-address>
					<nh-reference-count>
						600203
					</nh-reference-count>
					<nh-kernel-id>
						0
					</nh-kernel-id>
					<gateway>
						***********
					</gateway>
					<nh-type>
						Router
					</nh-type>
					<nh-index>
						1243
					</nh-index>
					<nh junos:indent="16">
						<nh-string>
							Next hop
						</nh-string>
						<to>
							***********
						</to>
						<via>
							et-0/0/2.3601
						</via>
						<selected-next-hop />
						<label-element>
							0x8b6e880
						</label-element>
						<label-element-parent>
							0x0
						</label-element-parent>
						<label-element-refcount>
							4
						</label-element-refcount>
						<label-element-childcount>
							2
						</label-element-childcount>
						<label-element-lspid>
							0
						</label-element-lspid>
						<session>
							532
						</session>
					</nh>
					<protocol-nh junos:indent="16">
						<to>
							***********
						</to>
						<indirect-nh>
							0xa010800 1048619 INH Session ID: 0x5c1
						</indirect-nh>
					</protocol-nh>
					<rt-entry-state>
						NotBest Int Ext
					</rt-entry-state>
					<inactive-reason>
						Not Best in its group - Router ID
					</inactive-reason>
					<local-as>
						14525
					</local-as>
					<peer-as>
						14525
					</peer-as>
					<age junos:seconds="242">
						4:02
					</age>
					<metric>
						0
					</metric>
					<metric2>
						1000
					</metric2>
					<validation-state>
						unverified
					</validation-state>
					<med-plus-igp>
						1000
					</med-plus-igp>
					<task-name>
						BGP_14525.***********
					</task-name>
					<as-path>
						AS path: 13335 I
						Aggregator: 13335 172.68.129.1
						AS path: Recorded
					</as-path>
					<bgp-path-attributes>
						<attr-as-path-effective>
							<aspath-effective-string>
								AS path:
							</aspath-effective-string>
							<attr-value>
								13335 I
							</attr-value>
							<flags>
							</flags>
						</attr-as-path-effective>
						<attr-aggregator>
							<attr-length>
								11
							</attr-length>
							<attr-value>
								<aggr-as-number>
									13335
								</aggr-as-number>
								<aggr-router-id>
									172.68.129.1
								</aggr-router-id>
							</attr-value>
						</attr-aggregator>
					</bgp-path-attributes>
					<communities>
						<community>
							13335:10232
						</community>
						<community>
							13335:19000
						</community>
						<community>
							13335:20050
						</community>
						<community>
							13335:20500
						</community>
						<community>
							13335:20530
						</community>
						<community>
							14525:0
						</community>
						<community>
							14525:20
						</community>
						<community>
							14525:600
						</community>
						<community>
							14525:1021
						</community>
						<community>
							14525:2840
						</community>
						<community>
							14525:3003
						</community>
						<community>
							14525:4003
						</community>
						<community>
							14525:9009
						</community>
					</communities>
					<bgp-rt-flag>
						Accepted
					</bgp-rt-flag>
					<local-preference>
						250
					</local-preference>
					<peer-id>
						***********
					</peer-id>
				</rt-entry>
				<rt-entry>
					<active-tag>
					</active-tag>
					<protocol-name>
						BGP
					</protocol-name>
					<preference>
						200
					</preference>
					<preference2>
						-251
					</preference2>
					<nh-type>
						Indirect
					</nh-type>
					<nh-index>
						0
					</nh-index>
					<nh-address>
						0x1e049e7c
					</nh-address>
					<nh-reference-count>
						135307
					</nh-reference-count>
					<nh-kernel-id>
						0
					</nh-kernel-id>
					<gateway>
						*************
					</gateway>
					<nh-type>
						Router
					</nh-type>
					<nh-index>
						0
					</nh-index>
					<nh junos:indent="16">
						<nh-string>
							Next hop
						</nh-string>
						<to>
							***********
						</to>
						<via>
							et-0/0/1.3605
						</via>
						<weight>
							0x1
						</weight>
						<selected-next-hop />
						<label-element>
							0x8b6f980
						</label-element>
						<label-element-parent>
							0x0
						</label-element-parent>
						<label-element-refcount>
							13
						</label-element-refcount>
						<label-element-childcount>
							5
						</label-element-childcount>
						<label-element-lspid>
							0
						</label-element-lspid>
						<session>
							0
						</session>
					</nh>
					<nh junos:indent="16">
						<nh-string>
							Next hop
						</nh-string>
						<to>
							100.64.0.140
						</to>
						<via>
							et-0/0/2.3607
						</via>
						<weight>
							0x1
						</weight>
						<label-element>
							0x8b6f980
						</label-element>
						<label-element-parent>
							0x0
						</label-element-parent>
						<label-element-refcount>
							13
						</label-element-refcount>
						<label-element-childcount>
							5
						</label-element-childcount>
						<label-element-lspid>
							0
						</label-element-lspid>
						<session>
							0
						</session>
					</nh>
					<protocol-nh junos:indent="16">
						<to>
							*************
						</to>
						<indirect-nh>
							0xa00f600 1048607 INH Session ID: 0x54c
						</indirect-nh>
					</protocol-nh>
					<rt-entry-state>
						NotBest Int Ext
					</rt-entry-state>
					<inactive-reason>
						Not Best in its group - Router ID
					</inactive-reason>
					<local-as>
						14525
					</local-as>
					<peer-as>
						14525
					</peer-as>
					<age junos:seconds="1367556">
						2w1d 19:52:36
					</age>
					<metric>
						0
					</metric>
					<metric2>
						1000
					</metric2>
					<validation-state>
						unverified
					</validation-state>
					<med-plus-igp>
						1000
					</med-plus-igp>
					<task-name>
						BGP_14525.*************
					</task-name>
					<as-path>
						AS path: 13335 I
						Aggregator: 13335 141.101.73.1
					</as-path>
					<bgp-path-attributes>
						<attr-as-path-effective>
							<aspath-effective-string>
								AS path:
							</aspath-effective-string>
							<attr-value>
								13335 I
							</attr-value>
						</attr-as-path-effective>
						<attr-aggregator>
							<attr-length>
								11
							</attr-length>
							<attr-value>
								<aggr-as-number>
									13335
								</aggr-as-number>
								<aggr-router-id>
									141.101.73.1
								</aggr-router-id>
							</attr-value>
						</attr-aggregator>
					</bgp-path-attributes>
					<communities>
						<community>
							13335:10014
						</community>
						<community>
							13335:19000
						</community>
						<community>
							13335:20050
						</community>
						<community>
							13335:20500
						</community>
						<community>
							13335:20530
						</community>
						<community>
							14525:0
						</community>
						<community>
							14525:20
						</community>
						<community>
							14525:600
						</community>
						<community>
							14525:1021
						</community>
						<community>
							14525:2840
						</community>
						<community>
							14525:3003
						</community>
						<community>
							14525:4002
						</community>
						<community>
							14525:9009
						</community>
					</communities>
					<bgp-rt-flag>
						Accepted
					</bgp-rt-flag>
					<local-preference>
						250
					</local-preference>
					<peer-id>
						*************
					</peer-id>
				</rt-entry>
			</rt>
		</route-table>
	</route-information>
	<cli>
		<banner>
		</banner>
	</cli>
</rpc-reply>
