{"name": "hyperglass-docs", "version": "2.0.0", "description": "hyperglass documentation", "private": true, "scripts": {"dev": "next dev", "start": "next start", "typecheck": "tsc --noEmit"}, "author": "thatmattlove <<EMAIL>>", "license": "BSD-3-<PERSON><PERSON>-<PERSON>", "dependencies": {"next": "^14.1.1", "nextra": "3.0.0-alpha.24", "nextra-theme-docs": "3.0.0-alpha.24", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/node": "^20.11.24", "typescript": "^5.3.3"}}