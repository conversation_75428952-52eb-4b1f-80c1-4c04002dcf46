Each device must be configured with credentials with which hyperglass can log into the device and execute commands.

## Credential Configuration

| Parameter             | Type   | Default Value | Description                                            |
| :-------------------- | :----- | :------------ | :----------------------------------------------------- |
| `credential.username` | String |               | Username to use for authentication to the device.      |
| `credential.password` | String |               | Password to use for authentication to the device.      |
| `credential.key`      | String |               | Path to SSH key used for authentication to the device. |

### Examples

#### Username & Password

```yaml filename="devices.yaml" copy {5-7}
devices:
    - name: New York, NY
      address: *********
      platform: cisco_ios
      credential:
          username: you
          password: your password
```

#### SSH Private Key

```yaml filename="devices.yaml" copy {5-7}
devices:
    - name: San Francisco, CA
      address: *********
      platform: juniper
      credential:
          username: you
          key: /path/to/your/ssh/key
```
