---
title: Introduction
description: Get started with hyperglass
---

import { Cards } from "nextra/components";
import { SupportedPlatforms } from "~/components/platforms";

## What is hyperglass?

<strong style={{ color: "#ff5e5b" }}>hyperglass</strong> is an open source network looking glass written
by a network engineer for other network engineers. The purpose of a looking glass is to provide customers,
peers, and complete strangers with unattended visibility into the an operator's network.

hyperglass was created with the lofty goal of benefiting the internet community at-large by providing a faster, easier, and more secure way for operators to provide looking glass services to their customers, peers, and other network operators.

## Features

-   BGP Route, BGP Community, BGP AS Path, Ping, & Traceroute
-   Full IPv6 support
-   Customizable everything: features, theme, UI/API text, error messages, commands
-   Built in support for:
    <SupportedPlatforms />
-   Configurable support for any other [supported platform](platforms.mdx)
-   Optionally access devices via an SSH proxy/jump server
-   VRF support
-   Access List/prefix-list style query control to whitelist or blacklist query targets on a per-VRF basis
-   REST API with automatic, configurable OpenAPI documentation
-   Modern, responsive UI
-   Query multiple devices simultaneously
-   Browser-based DNS-over-HTTPS resolution of FQDN queries

<Cards>
    <Cards.Card title="Get Started" href="installation/docker" arrow />
</Cards>
