---
title: Manual Installation
description: Installing hyperglass manually
---

import { Steps, Callout } from "nextra/components";

<Steps>

### Install Dependencies

To install hyperglass manually, you'll need to install the following dependencies:

1. [Python 3.11, or 3.12](https://www.python.org/downloads/) and [`pip`](https://pip.pypa.io/en/stable/installation/)
2. [NodeJS 20.14 or later](https://nodejs.org/en/download)
3. [PNPM 8 or later](https://pnpm.io/installation)
4. [Redis 7.2 or later](https://redis.io/download/)

<Callout type="warning">Make sure the Redis server is started.</Callout>

### Install hyperglass

Once these dependencies are installed, install hyperglass via PyPI:

```shell copy
git clone https://github.com/thatmattlove/hyperglass --depth=1
cd hyperglass
pip3 install -e .
```

### Create app directory

<Callout type="info">
    If you plan on using a different directory, be sure to set the directory you wish to use in your
    [environment variables](environment-variables.mdx).
</Callout>

```shell copy
mkdir /etc/hyperglass
```

### Optional: Quickstart

Do this if you just want to see the hyperglass page working with default settings and a fake device.

```shell copy
curl -o /etc/hyperglass/devices.yaml https://raw.githubusercontent.com/thatmattlove/hyperglass/main/.samples/sample_devices.yaml
hyperglass start
```

### Create a `systemd` service

```shell copy
curl -o /etc/hyperglass/hyperglass.service https://raw.githubusercontent.com/thatmattlove/hyperglass/main/.samples/hyperglass-manual.service
ln -s /etc/hyperglass/hyperglass.service /etc/systemd/system/hyperglass.service
systemctl daemon-reload
systemctl enable hyperglass
systemctl start hyperglass
```

<Callout type="info">
    If you used a different app directory from the default `/etc/hyperglass`, change the
    `EnvironmentFile` value in the `hyperglass.service` file.
</Callout>

</Steps>
