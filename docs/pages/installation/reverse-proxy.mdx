---
title: Reverse Proxy
description: Setting up a reverse proxy for hyperglass
---

import { Cards, Callout } from "nextra/components";

[<PERSON><PERSON><PERSON>](https://caddyserver.com) is recommended, but any reverse proxy ([NGINX](https://www.nginx.com), [Apache2](https://httpd.apache.org)) will work.

## Caddy

<Cards>
    <Cards.Card
        title="Install Caddy"
        target="_blank"
        href="https://caddyserver.com/docs/install"
        arrow
    />
</Cards>

```shell copy
cp /opt/hyperglass/.samples/Caddyfile /etc/caddy/Caddyfile
```

Change the `lg.example.com` and `<EMAIL>` values to match your hyperglass FQDN and email address (the email address is for automatic SSL certificate generation via Let's Encrypt).

<Callout type="info">
    If you prefer to use other Let's Encrypt validation methods or your own SSL certificate, modify
    your `/etc/hyperglass/Caddyfile` in accordance with the [Caddy
    docs](https://caddyserver.com/docs/caddyfile-tutorial).
</Callout>

Restart the Caddy service: `systemctl restart caddy{:shell}`

## NGINX

```shell copy
cp /opt/hyperglass/.samples/hyperglass.nginx /etc/nginx/sites-available/hyperglass
ln -s /etc/nginx/sites-available/hyperglass /etc/nginx/sites-enabled/hyperglass
```

Change the `lg.example.com` value to match your hyperglass FQDN.

Change the `<path to cert chain>` and `<path to key>` values to match the path to your certificate and private key files.
