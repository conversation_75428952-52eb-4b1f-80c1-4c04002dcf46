[{"name": "Arista EOS", "keys": ["arista_eos"], "native": true}, {"name": "BIRD", "keys": ["bird"], "native": true}, {"name": "Cisco IOS", "keys": ["cisco_ios"], "native": true}, {"name": "Cisco NX-OS", "keys": ["cisco_nxos"], "native": true}, {"name": "Cisco IOS-XR", "keys": ["cisco_xr"], "native": true}, {"name": "FRRouting", "keys": ["frr"], "native": true}, {"name": "Huawei VRP", "keys": ["hua<PERSON>", "huawei_vrpv8"], "native": true}, {"name": "Juniper Junos", "keys": ["juniper", "juniper_junos"], "native": true}, {"name": "Mikrotik", "keys": ["mikrotik_routeros", "mikrot<PERSON>_switchos"], "native": true}, {"name": "Nokia SR OS", "keys": ["nokia_sros"], "native": true}, {"name": "OpenBGPD", "keys": ["openbgpd"], "native": true}, {"name": "TNSR", "keys": ["tnsr"], "native": true}, {"name": "VyOS", "keys": ["vyos"], "native": true}, {"name": "", "keys": ["a10"], "native": false}, {"name": "", "keys": ["a10_ssh"], "native": false}, {"name": "", "keys": ["accedian"], "native": false}, {"name": "", "keys": ["accedian_ssh"], "native": false}, {"name": "", "keys": ["adtran_os"], "native": false}, {"name": "", "keys": ["adtran_os_ssh"], "native": false}, {"name": "", "keys": ["adtran_os_telnet"], "native": false}, {"name": "", "keys": ["alcatel_aos"], "native": false}, {"name": "", "keys": ["alcatel_aos_ssh"], "native": false}, {"name": "", "keys": ["alcatel_sros"], "native": false}, {"name": "", "keys": ["alcatel_sros_ssh"], "native": false}, {"name": "", "keys": ["allied_telesis_awplus"], "native": false}, {"name": "", "keys": ["allied_telesis_awplus_ssh"], "native": false}, {"name": "", "keys": ["apresia_aeos"], "native": false}, {"name": "", "keys": ["apresia_aeos_ssh"], "native": false}, {"name": "", "keys": ["apresia_aeos_telnet"], "native": false}, {"name": "", "keys": ["arista_eos_ssh"], "native": false}, {"name": "", "keys": ["arista_eos_telnet"], "native": false}, {"name": "", "keys": ["aruba_os"], "native": false}, {"name": "", "keys": ["aruba_os_ssh"], "native": false}, {"name": "", "keys": ["aruba_osswitch"], "native": false}, {"name": "", "keys": ["aruba_osswitch_ssh"], "native": false}, {"name": "", "keys": ["aruba_procurve"], "native": false}, {"name": "", "keys": ["aruba_procurve_ssh"], "native": false}, {"name": "", "keys": ["aruba_procurve_telnet"], "native": false}, {"name": "", "keys": ["audiocode_66"], "native": false}, {"name": "", "keys": ["audiocode_66_ssh"], "native": false}, {"name": "", "keys": ["audiocode_66_telnet"], "native": false}, {"name": "", "keys": ["audiocode_72"], "native": false}, {"name": "", "keys": ["audiocode_72_ssh"], "native": false}, {"name": "", "keys": ["audiocode_72_telnet"], "native": false}, {"name": "", "keys": ["audiocode_shell"], "native": false}, {"name": "", "keys": ["audiocode_shell_ssh"], "native": false}, {"name": "", "keys": ["audiocode_shell_telnet"], "native": false}, {"name": "", "keys": ["autodetect"], "native": false}, {"name": "", "keys": ["avaya_ers"], "native": false}, {"name": "", "keys": ["avaya_ers_ssh"], "native": false}, {"name": "", "keys": ["avaya_vsp"], "native": false}, {"name": "", "keys": ["avaya_vsp_ssh"], "native": false}, {"name": "", "keys": ["broadcom_icos"], "native": false}, {"name": "", "keys": ["broadcom_icos_ssh"], "native": false}, {"name": "", "keys": ["brocade_fastiron"], "native": false}, {"name": "", "keys": ["brocade_fastiron_ssh"], "native": false}, {"name": "", "keys": ["brocade_fastiron_telnet"], "native": false}, {"name": "", "keys": ["brocade_fos"], "native": false}, {"name": "", "keys": ["brocade_fos_ssh"], "native": false}, {"name": "", "keys": ["brocade_netiron"], "native": false}, {"name": "", "keys": ["brocade_netiron_ssh"], "native": false}, {"name": "", "keys": ["brocade_netiron_telnet"], "native": false}, {"name": "", "keys": ["brocade_nos"], "native": false}, {"name": "", "keys": ["brocade_nos_ssh"], "native": false}, {"name": "", "keys": ["brocade_vdx"], "native": false}, {"name": "", "keys": ["brocade_vdx_ssh"], "native": false}, {"name": "", "keys": ["brocade_vyos"], "native": false}, {"name": "", "keys": ["brocade_vyos_ssh"], "native": false}, {"name": "", "keys": ["calix_b6"], "native": false}, {"name": "", "keys": ["calix_b6_ssh"], "native": false}, {"name": "", "keys": ["calix_b6_telnet"], "native": false}, {"name": "", "keys": ["cdot_cros"], "native": false}, {"name": "", "keys": ["cdot_cros_ssh"], "native": false}, {"name": "", "keys": ["centec_os"], "native": false}, {"name": "", "keys": ["centec_os_ssh"], "native": false}, {"name": "", "keys": ["centec_os_telnet"], "native": false}, {"name": "", "keys": ["checkpoint_gaia"], "native": false}, {"name": "", "keys": ["checkpoint_gaia_ssh"], "native": false}, {"name": "", "keys": ["ciena_saos"], "native": false}, {"name": "", "keys": ["ciena_saos_ssh"], "native": false}, {"name": "", "keys": ["ciena_saos_telnet"], "native": false}, {"name": "", "keys": ["cisco_asa"], "native": false}, {"name": "", "keys": ["cisco_asa_ssh"], "native": false}, {"name": "", "keys": ["cisco_ftd"], "native": false}, {"name": "", "keys": ["cisco_ftd_ssh"], "native": false}, {"name": "", "keys": ["cisco_ios_serial"], "native": false}, {"name": "", "keys": ["cisco_ios_ssh"], "native": false}, {"name": "", "keys": ["cisco_ios_telnet"], "native": false}, {"name": "", "keys": ["cisco_nxos_ssh"], "native": false}, {"name": "", "keys": ["cisco_s300"], "native": false}, {"name": "", "keys": ["cisco_s300_ssh"], "native": false}, {"name": "", "keys": ["cisco_s300_telnet"], "native": false}, {"name": "", "keys": ["cisco_tp"], "native": false}, {"name": "", "keys": ["cisco_tp_ssh"], "native": false}, {"name": "", "keys": ["cisco_viptela"], "native": false}, {"name": "", "keys": ["cisco_viptela_ssh"], "native": false}, {"name": "", "keys": ["cisco_wlc"], "native": false}, {"name": "", "keys": ["cisco_wlc_ssh"], "native": false}, {"name": "", "keys": ["cisco_xe"], "native": false}, {"name": "", "keys": ["cisco_xe_ssh"], "native": false}, {"name": "", "keys": ["cisco_xr_ssh"], "native": false}, {"name": "", "keys": ["cisco_xr_telnet"], "native": false}, {"name": "", "keys": ["cloudgenix_ion"], "native": false}, {"name": "", "keys": ["cloudgenix_ion_ssh"], "native": false}, {"name": "", "keys": ["coriant"], "native": false}, {"name": "", "keys": ["coriant_ssh"], "native": false}, {"name": "", "keys": ["dell_dnos6_telnet"], "native": false}, {"name": "", "keys": ["dell_dnos9"], "native": false}, {"name": "", "keys": ["dell_dnos9_ssh"], "native": false}, {"name": "", "keys": ["dell_force10"], "native": false}, {"name": "", "keys": ["dell_force10_ssh"], "native": false}, {"name": "", "keys": ["dell_isilon"], "native": false}, {"name": "", "keys": ["dell_isilon_ssh"], "native": false}, {"name": "", "keys": ["dell_os10"], "native": false}, {"name": "", "keys": ["dell_os10_ssh"], "native": false}, {"name": "", "keys": ["dell_os6"], "native": false}, {"name": "", "keys": ["dell_os6_ssh"], "native": false}, {"name": "", "keys": ["dell_os9"], "native": false}, {"name": "", "keys": ["dell_os9_ssh"], "native": false}, {"name": "", "keys": ["dell_powerconnect"], "native": false}, {"name": "", "keys": ["dell_powerconnect_ssh"], "native": false}, {"name": "", "keys": ["dell_powerconnect_telnet"], "native": false}, {"name": "", "keys": ["dell_sonic"], "native": false}, {"name": "", "keys": ["dell_sonic_ssh"], "native": false}, {"name": "", "keys": ["dlink_ds"], "native": false}, {"name": "", "keys": ["dlink_ds_ssh"], "native": false}, {"name": "", "keys": ["dlink_ds_telnet"], "native": false}, {"name": "", "keys": ["eltex"], "native": false}, {"name": "", "keys": ["eltex_esr"], "native": false}, {"name": "", "keys": ["eltex_esr_ssh"], "native": false}, {"name": "", "keys": ["eltex_ssh"], "native": false}, {"name": "", "keys": ["endace"], "native": false}, {"name": "", "keys": ["endace_ssh"], "native": false}, {"name": "", "keys": ["enterasys"], "native": false}, {"name": "", "keys": ["enterasys_ssh"], "native": false}, {"name": "", "keys": ["er<PERSON><PERSON>_ipos"], "native": false}, {"name": "", "keys": ["er<PERSON><PERSON>_ipos_ssh"], "native": false}, {"name": "", "keys": ["extreme"], "native": false}, {"name": "", "keys": ["extreme_ers"], "native": false}, {"name": "", "keys": ["extreme_ers_ssh"], "native": false}, {"name": "", "keys": ["extreme_exos"], "native": false}, {"name": "", "keys": ["extreme_exos_ssh"], "native": false}, {"name": "", "keys": ["extreme_exos_telnet"], "native": false}, {"name": "", "keys": ["extreme_netiron"], "native": false}, {"name": "", "keys": ["extreme_netiron_ssh"], "native": false}, {"name": "", "keys": ["extreme_netiron_telnet"], "native": false}, {"name": "", "keys": ["extreme_nos"], "native": false}, {"name": "", "keys": ["extreme_nos_ssh"], "native": false}, {"name": "", "keys": ["extreme_slx"], "native": false}, {"name": "", "keys": ["extreme_slx_ssh"], "native": false}, {"name": "", "keys": ["extreme_ssh"], "native": false}, {"name": "", "keys": ["extreme_telnet"], "native": false}, {"name": "", "keys": ["extreme_tierra"], "native": false}, {"name": "", "keys": ["extreme_tierra_ssh"], "native": false}, {"name": "", "keys": ["extreme_vdx"], "native": false}, {"name": "", "keys": ["extreme_vdx_ssh"], "native": false}, {"name": "", "keys": ["extreme_vsp"], "native": false}, {"name": "", "keys": ["extreme_vsp_ssh"], "native": false}, {"name": "", "keys": ["extreme_wing"], "native": false}, {"name": "", "keys": ["extreme_wing_ssh"], "native": false}, {"name": "", "keys": ["f5_linux"], "native": false}, {"name": "", "keys": ["f5_linux_ssh"], "native": false}, {"name": "", "keys": ["f5_ltm"], "native": false}, {"name": "", "keys": ["f5_ltm_ssh"], "native": false}, {"name": "", "keys": ["f5_tmsh"], "native": false}, {"name": "", "keys": ["f5_tmsh_ssh"], "native": false}, {"name": "", "keys": ["flexvnf"], "native": false}, {"name": "", "keys": ["flexvnf_ssh"], "native": false}, {"name": "", "keys": ["fortinet"], "native": false}, {"name": "", "keys": ["fortinet_ssh"], "native": false}, {"name": "", "keys": ["generic"], "native": false}, {"name": "", "keys": ["generic_ssh"], "native": false}, {"name": "", "keys": ["generic_telnet"], "native": false}, {"name": "", "keys": ["generic_termserver"], "native": false}, {"name": "", "keys": ["generic_termserver_ssh"], "native": false}, {"name": "", "keys": ["generic_termserver_telnet"], "native": false}, {"name": "", "keys": ["hp_comware"], "native": false}, {"name": "", "keys": ["hp_comware_ssh"], "native": false}, {"name": "", "keys": ["hp_comware_telnet"], "native": false}, {"name": "", "keys": ["hp_procurve"], "native": false}, {"name": "", "keys": ["hp_procurve_ssh"], "native": false}, {"name": "", "keys": ["hp_procurve_telnet"], "native": false}, {"name": "", "keys": ["huawei_olt"], "native": false}, {"name": "", "keys": ["huawei_olt_ssh"], "native": false}, {"name": "", "keys": ["huawei_olt_telnet"], "native": false}, {"name": "", "keys": ["huawei_smartax"], "native": false}, {"name": "", "keys": ["huawei_smartax_ssh"], "native": false}, {"name": "", "keys": ["huawei_ssh"], "native": false}, {"name": "", "keys": ["huawei_telnet"], "native": false}, {"name": "", "keys": ["huawei_vrpv8_ssh"], "native": false}, {"name": "", "keys": ["ipinfusion_ocnos"], "native": false}, {"name": "", "keys": ["ipinfusion_ocnos_ssh"], "native": false}, {"name": "", "keys": ["ipinfusion_ocnos_telnet"], "native": false}, {"name": "", "keys": ["juniper_junos_ssh"], "native": false}, {"name": "", "keys": ["juniper_junos_telnet"], "native": false}, {"name": "", "keys": ["juniper_screenos"], "native": false}, {"name": "", "keys": ["juniper_screenos_ssh"], "native": false}, {"name": "", "keys": ["juniper_ssh"], "native": false}, {"name": "", "keys": ["keymile"], "native": false}, {"name": "", "keys": ["keymile_nos"], "native": false}, {"name": "", "keys": ["keymile_nos_ssh"], "native": false}, {"name": "", "keys": ["keymile_ssh"], "native": false}, {"name": "", "keys": ["linux"], "native": false}, {"name": "", "keys": ["linux_ssh"], "native": false}, {"name": "", "keys": ["<PERSON><PERSON><PERSON>"], "native": false}, {"name": "", "keys": ["mellanox_mlnxos"], "native": false}, {"name": "", "keys": ["mellanox_mlnxos_ssh"], "native": false}, {"name": "", "keys": ["mellanox_ssh"], "native": false}, {"name": "", "keys": ["mikrotik_routeros_ssh"], "native": false}, {"name": "", "keys": ["mikrot<PERSON>_switchos_ssh"], "native": false}, {"name": "", "keys": ["mrv_lx"], "native": false}, {"name": "", "keys": ["mrv_lx_ssh"], "native": false}, {"name": "", "keys": ["mrv_optiswitch"], "native": false}, {"name": "", "keys": ["mrv_optiswitch_ssh"], "native": false}, {"name": "", "keys": ["netapp_cdot"], "native": false}, {"name": "", "keys": ["netapp_cdot_ssh"], "native": false}, {"name": "", "keys": ["netgear_prosafe"], "native": false}, {"name": "", "keys": ["netgear_prosafe_ssh"], "native": false}, {"name": "", "keys": ["netscaler"], "native": false}, {"name": "", "keys": ["netscaler_ssh"], "native": false}, {"name": "", "keys": ["nokia_srl"], "native": false}, {"name": "", "keys": ["nokia_srl_ssh"], "native": false}, {"name": "", "keys": ["nokia_sros_ssh"], "native": false}, {"name": "", "keys": ["nokia_sros_telnet"], "native": false}, {"name": "", "keys": ["oneaccess_oneos"], "native": false}, {"name": "", "keys": ["oneaccess_oneos_ssh"], "native": false}, {"name": "", "keys": ["oneaccess_oneos_telnet"], "native": false}, {"name": "", "keys": ["ovs_linux"], "native": false}, {"name": "", "keys": ["ovs_linux_ssh"], "native": false}, {"name": "", "keys": ["paloalto_panos"], "native": false}, {"name": "", "keys": ["paloalto_panos_ssh"], "native": false}, {"name": "", "keys": ["paloalto_panos_telnet"], "native": false}, {"name": "", "keys": ["pluribus"], "native": false}, {"name": "", "keys": ["pluribus_ssh"], "native": false}, {"name": "", "keys": ["quanta_mesh"], "native": false}, {"name": "", "keys": ["quanta_mesh_ssh"], "native": false}, {"name": "", "keys": ["rad_etx"], "native": false}, {"name": "", "keys": ["rad_etx_ssh"], "native": false}, {"name": "", "keys": ["rad_etx_telnet"], "native": false}, {"name": "", "keys": ["raisecom_roap"], "native": false}, {"name": "", "keys": ["raisecom_roap_ssh"], "native": false}, {"name": "", "keys": ["raisecom_telnet"], "native": false}, {"name": "", "keys": ["ruckus_fastiron"], "native": false}, {"name": "", "keys": ["ruckus_fastiron_ssh"], "native": false}, {"name": "", "keys": ["ruckus_fastiron_telnet"], "native": false}, {"name": "", "keys": ["ruijie_os"], "native": false}, {"name": "", "keys": ["ruijie_os_ssh"], "native": false}, {"name": "", "keys": ["ruijie_os_telnet"], "native": false}, {"name": "", "keys": ["sixwind_os"], "native": false}, {"name": "", "keys": ["sixwind_os_ssh"], "native": false}, {"name": "", "keys": ["sophos_sfos"], "native": false}, {"name": "", "keys": ["sophos_sfos_ssh"], "native": false}, {"name": "", "keys": ["supermicro_smis"], "native": false}, {"name": "", "keys": ["supermicro_smis_ssh"], "native": false}, {"name": "", "keys": ["supermicro_smis_telnet"], "native": false}, {"name": "", "keys": ["terminal_server"], "native": false}, {"name": "", "keys": ["tplink_jetstream"], "native": false}, {"name": "", "keys": ["tplink_jetstream_ssh"], "native": false}, {"name": "", "keys": ["tplink_jetstream_telnet"], "native": false}, {"name": "", "keys": ["ubiquiti_edge"], "native": false}, {"name": "", "keys": ["ubiquiti_edge_ssh"], "native": false}, {"name": "", "keys": ["ubiquiti_edgerouter"], "native": false}, {"name": "", "keys": ["ubiquiti_edgerouter_ssh"], "native": false}, {"name": "", "keys": ["ubiquiti_edgeswitch"], "native": false}, {"name": "", "keys": ["ubiquiti_edgeswitch_ssh"], "native": false}, {"name": "", "keys": ["ubiquiti_unifiswitch"], "native": false}, {"name": "", "keys": ["ubiquiti_unifiswitch_ssh"], "native": false}, {"name": "", "keys": ["vyatta_vyos"], "native": false}, {"name": "", "keys": ["vyatta_vyos_ssh"], "native": false}, {"name": "", "keys": ["vyos_ssh"], "native": false}, {"name": "", "keys": ["watchguard_fireware"], "native": false}, {"name": "", "keys": ["watchguard_fireware_ssh"], "native": false}, {"name": "", "keys": ["yamaha"], "native": false}, {"name": "", "keys": ["yamaha_ssh"], "native": false}, {"name": "", "keys": ["yamaha_telnet"], "native": false}, {"name": "", "keys": ["zte_zxros"], "native": false}, {"name": "", "keys": ["zte_zxros_ssh"], "native": false}, {"name": "", "keys": ["zte_zxros_telnet"], "native": false}, {"name": "", "keys": ["zyxel_os"], "native": false}, {"name": "", "keys": ["zyxel_os_ssh"], "native": false}]