{"compilerOptions": {"target": "ESNext", "module": "esnext", "downlevelIteration": true, "strict": true, "baseUrl": ".", "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "noEmit": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "paths": {"~/*": ["./*"]}}, "exclude": ["node_modules", ".next"], "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", "next.config.mjs"]}