{"version": "2.0.4", "name": "ui", "description": "UI for hyperglass, the modern network looking glass", "author": "<PERSON>", "license": "BSD-3-<PERSON><PERSON>-<PERSON>", "private": true, "scripts": {"lint": "biome lint .", "dev": "next dev", "start": "next start", "typecheck": "tsc --noEmit", "format": "biome format --write .", "format:check": "biome format .", "build": "export NODE_OPTIONS=--openssl-legacy-provider; next build && next export --no-lint -o ../hyperglass/static/ui", "test": "vitest --run"}, "browserslist": "> 0.25%, not dead", "dependencies": {"@chakra-ui/react": "^2.8.2", "@chakra-ui/theme": "3.3.1", "@chakra-ui/theme-tools": "^2.1.2", "@chakra-ui/utils": "^2.0.14", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.0", "@hookform/devtools": "^4.3.0", "@hookform/resolvers": "^2.9.10", "@tanstack/react-query": "^4.22.0", "dagre": "^0.8.5", "dayjs": "^1.10.4", "framer-motion": "^11.2.10", "lodash": "^4.17.21", "merge-anything": "^4.0.1", "next": "13.5.6", "palette-by-numbers": "^0.1.6", "plur": "^4.0.0", "react": "^18.2.0", "react-countdown": "^2.3.0", "react-device-detect": "^1.15.0", "react-dom": "^18.2.0", "react-fast-compare": "^3.2.1", "react-hook-form": "^7.42.1", "react-icons": "^4.3.1", "react-if": "^4.1.4", "react-markdown": "^5.0.3", "react-select": "^5.7.0", "react-string-replace": "^1.1.1", "react-table": "^7.7.0", "reactflow": "^11.10.4", "remark-gfm": "^1.0.0", "string-format": "^2.0.0", "vest": "^3.2.8", "zustand": "^3.7.2"}, "devDependencies": {"@biomejs/biome": "1.5.3", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@testing-library/react-hooks": "^8.0.1", "@testing-library/user-event": "^14.5.2", "@types/dagre": "^0.7.44", "@types/lodash": "^4.14.177", "@types/node": "^20.11.20", "@types/react": "^18.2.60", "@types/react-table": "^7.7.1", "@types/string-format": "^2.0.0", "@typescript-eslint/eslint-plugin": "^7.1.0", "@typescript-eslint/parser": "^7.1.0", "@vitejs/plugin-react": "^4.2.1", "@vitest/ui": "^1.3.1", "babel-eslint": "^10.1.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-json": "^3.1.0", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "identity-obj-proxy": "^3.0.0", "isomorphic-fetch": "^3.0.0", "jsdom": "^24.0.0", "prettier": "^3.2.5", "prettier-eslint": "^16.3.0", "react-test-renderer": "^18.2.0", "type-fest": "^4.10.3", "typescript": "^5.3.3", "vitest": "^1.3.1"}}